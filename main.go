package main

import (
	"bytes"
	"fmt"
	"net/http"
	"time"
)

func main() {
	url := "http://localhost:8023/api/v1/session/770b8368-b827-4a40-925e-fb45902ddcea/check-device"
	apiKey := "saye"

	// 60 kere yolla
	for i := 1; i <= 60; i++ {
		go func(i int) {
			req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte{}))
			if err != nil {
				fmt.Println("Request oluşturulamadı:", err)
				return
			}

			req.Header.Set("X-API-Key", apiKey)
			req.Header.Set("Content-Type", "application/json")

			client := &http.Client{}
			resp, err := client.Do(req)
			if err != nil {
				fmt.Println("Request hatası:", err)
				return
			}
			defer resp.Body.Close()

			fmt.Printf("[%d] Status: %s\n", i, resp.Status)
		}(i)

		time.Sleep(1 * time.Second) // Her saniye 1 istek
	}
}
