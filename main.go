package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

// WhatsApp API Response structures
type WhatsAppResponse struct {
	Connected bool        `json:"connected"`
	Data      SessionData `json:"data"`
	RegID     string      `json:"reg_id"`
	Session   Session     `json:"session"`
	SessionID string      `json:"session_id"`
}

type SessionData struct {
	RegID      string    `json:"reg_id"`
	CreatedAt  time.Time `json:"created_at"`
	Platform   string    `json:"platform"`
	PushName   string    `json:"push_name"`
	IsLoggedIn bool      `json:"is_logged_in"`
}

type Session struct {
	ID                 string    `json:"id"`
	RegID              string    `json:"reg_id"`
	JID                string    `json:"jid"`
	Status             string    `json:"status"`
	LastConnected      time.Time `json:"last_connected"`
	LastDisconnected   time.Time `json:"last_disconnected"`
	ConnectionAttempts int       `json:"connection_attempts"`
	ErrorMessage       string    `json:"error_message"`
	ProxyUsed          bool      `json:"proxy_used"`
	AutoReconnect      bool      `json:"auto_reconnect"`
	MessageCount       int       `json:"message_count"`
	CreatedAt          time.Time `json:"created_at"`
	IP                 string    `json:"ip"`
}

func main() {
	url := "http://localhost:8023/api/v1/session/770b8368-b827-4a40-925e-fb45902ddcea/check-device"
	apiKey := "saye"

	// Use a mutex to ensure requests don't overlap
	var mu sync.Mutex
	var wg sync.WaitGroup

	for i := 1; i <= 60; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()

			// Lock to ensure only one request at a time
			mu.Lock()
			defer mu.Unlock()

			fmt.Printf("[%d] Starting request...\n", i)

			req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte{}))
			if err != nil {
				fmt.Printf("[%d] Request oluşturulamadı: %v\n", i, err)
				return
			}

			req.Header.Set("X-API-Key", apiKey)
			req.Header.Set("Content-Type", "application/json")

			client := &http.Client{
				Timeout: 30 * time.Second, // Add timeout
			}
			resp, err := client.Do(req)
			if err != nil {
				fmt.Printf("[%d] Request hatası: %v\n", i, err)
				return
			}
			defer resp.Body.Close()

			body, err := io.ReadAll(resp.Body)
			if err != nil {
				fmt.Printf("[%d] Response okunamadı: %v\n", i, err)
				return
			}

			fmt.Printf("[%d] Status: %s\n", i, resp.Status)

			// Try to parse the JSON response
			var whatsappResp WhatsAppResponse
			if err := json.Unmarshal(body, &whatsappResp); err != nil {
				fmt.Printf("[%d] JSON parse hatası: %v\n", i, err)
				fmt.Printf("[%d] Raw Body: %s\n", i, string(body))
			} else {
				fmt.Printf("[%d] Parsed Response:\n", i)
				fmt.Printf("    Connected: %t\n", whatsappResp.Connected)
				fmt.Printf("    Reg ID: %s\n", whatsappResp.RegID)
				fmt.Printf("    Session ID: %s\n", whatsappResp.SessionID)
				fmt.Printf("    Session Status: %s\n", whatsappResp.Session.Status)
				fmt.Printf("    Is Logged In: %t\n", whatsappResp.Data.IsLoggedIn)
				fmt.Printf("    JID: %s\n", whatsappResp.Session.JID)
			}

			fmt.Printf("[%d] Request completed\n", i)
		}(i)

		// Small delay between starting goroutines
		time.Sleep(50 * time.Millisecond)
	}

	// Wait for all requests to complete
	wg.Wait()
	fmt.Println("All requests completed")
}
